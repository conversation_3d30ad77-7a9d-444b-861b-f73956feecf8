"""
稳定版8浏览器并发处理器 🚀
解决文件冲突问题，为每个分片创建独立的工作目录

特性：
- 每个分片在独立目录中处理，避免文件冲突
- 8个浏览器实例同时工作
- 自动任务分配和进度监控
- 完善的错误处理和恢复机制
"""

import os
import sys
import time
import threading
import random
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
from integrated_restaurant_processor import process_restaurants_with_browser
import glob
from typing import List, Dict


class Stable8BrowserProcessor:
    """稳定版8浏览器并发处理器"""
    
    def __init__(self, max_browsers=8):
        self.max_browsers = max_browsers
        self.results = {}
        self.lock = threading.Lock()
        self.start_time = None
        self.processed_count = 0
        self.total_tasks = 0
        self.work_dir = "browser_work_dirs"
        
        # 创建工作目录
        if not os.path.exists(self.work_dir):
            os.makedirs(self.work_dir)
        
        print(f"🚀 稳定版8浏览器并发处理器启动！")
        print(f"⚡ 浏览器数量: {max_browsers}")
        print(f"📁 工作目录: {self.work_dir}")
    
    def find_shard_files(self) -> Dict[str, List[str]]:
        """查找所有分片文件"""
        shard_files = {}
        countries = ["印度尼西亚", "菲律宾", "马来西亚"]
        
        for country in countries:
            shard_dir = f"{country}_分片数据"
            if os.path.exists(shard_dir):
                pattern = os.path.join(shard_dir, f"{country}_restaurant_urls_第*份.csv")
                files = glob.glob(pattern)
                if files:
                    files.sort(key=lambda x: int(x.split('第')[1].split('份')[0]))
                    shard_files[country] = files
                    print(f"📁 发现 {country}: {len(files)} 个分片文件")
        
        return shard_files
    
    def create_processing_tasks(self, shard_files: Dict[str, List[str]]) -> List[Dict]:
        """创建处理任务列表"""
        tasks = []
        
        for country, files in shard_files.items():
            for file_path in files:
                shard_num = file_path.split('第')[1].split('份')[0]
                
                task = {
                    'country': country,
                    'shard_file': file_path,
                    'shard_num': shard_num,
                    'task_id': f"{country}_第{shard_num}份",
                    'work_dir': os.path.join(self.work_dir, f"{country}_第{shard_num}份"),
                    'output_file': f"{country}_restaurant_details_第{shard_num}份_translated.csv"
                }
                tasks.append(task)
        
        return tasks
    
    def setup_work_directory(self, task: Dict) -> bool:
        """为任务设置独立的工作目录"""
        work_dir = task['work_dir']
        country = task['country']
        shard_file = task['shard_file']
        
        try:
            # 创建工作目录
            if os.path.exists(work_dir):
                shutil.rmtree(work_dir)
            os.makedirs(work_dir)
            
            # 复制分片文件到工作目录
            target_file = os.path.join(work_dir, f"{country}_restaurant_urls.csv")
            shutil.copy2(shard_file, target_file)
            
            # 复制必要的脚本文件
            script_files = [
                "integrated_restaurant_processor.py",
                "__init__.py"
            ]
            
            for script in script_files:
                if os.path.exists(script):
                    shutil.copy2(script, work_dir)
            
            # 复制翻译缓存文件（如果存在）
            cache_file = f"{country}_translation_cache.json"
            if os.path.exists(cache_file):
                shutil.copy2(cache_file, work_dir)
            
            return True
            
        except Exception as e:
            print(f"❌ 设置工作目录失败 [{task['task_id']}]: {e}")
            return False
    
    def process_single_shard(self, task: Dict) -> Dict:
        """处理单个分片文件"""
        task_id = task['task_id']
        country = task['country']
        work_dir = task['work_dir']
        
        start_time = time.time()
        thread_id = threading.current_thread().ident % 100
        
        print(f"🌍 [浏览器{thread_id}] 开始处理: {task_id}")
        
        try:
            # 设置工作目录
            if not self.setup_work_directory(task):
                raise Exception("工作目录设置失败")
            
            # 切换到工作目录
            original_cwd = os.getcwd()
            os.chdir(work_dir)
            
            try:
                # 调用处理函数
                process_restaurants_with_browser(country)
                
                # 检查输出文件
                output_file = f"{country}_restaurant_details_translated.csv"
                if os.path.exists(output_file):
                    # 移动输出文件到主目录
                    final_output = os.path.join(original_cwd, task['output_file'])
                    shutil.move(output_file, final_output)
                    print(f"✅ [{task_id}] 输出文件: {task['output_file']}")
                    
                    # 复制更新的缓存文件
                    cache_file = f"{country}_translation_cache.json"
                    if os.path.exists(cache_file):
                        main_cache = os.path.join(original_cwd, cache_file)
                        shutil.copy2(cache_file, main_cache)
                
            finally:
                # 恢复原工作目录
                os.chdir(original_cwd)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 更新统计
            with self.lock:
                self.processed_count += 1
                progress = (self.processed_count / self.total_tasks) * 100
                print(f"🎉 [{task_id}] 完成！耗时: {duration/60:.1f}分钟 | 总进度: {progress:.1f}%")
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'duration': duration,
                'output_file': task['output_file'],
                'start_time': start_time,
                'end_time': end_time
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ [{task_id}] 处理失败: {e}")
            
            return {
                'task_id': task_id,
                'status': 'failed',
                'error': str(e),
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time
            }
        
        finally:
            # 清理工作目录
            try:
                if os.path.exists(work_dir):
                    shutil.rmtree(work_dir)
            except:
                pass
    
    def run_stable_processing(self):
        """开始稳定处理"""
        print("\n" + "🔥" * 60)
        print("🚀 稳定版8浏览器并发处理开始！")
        print("🔥" * 60)
        
        # 查找分片文件
        shard_files = self.find_shard_files()
        if not shard_files:
            print("❌ 没有找到分片文件，请先运行数据拆分脚本")
            return
        
        # 创建任务列表
        tasks = self.create_processing_tasks(shard_files)
        self.total_tasks = len(tasks)
        
        print(f"\n📊 任务统计:")
        for country, files in shard_files.items():
            print(f"  {country}: {len(files)} 个分片")
        print(f"  总任务数: {self.total_tasks}")
        
        # 随机打乱任务顺序
        random.shuffle(tasks)
        
        print(f"\n🤔 准备用 {self.max_browsers} 个浏览器稳定处理这些任务...")
        print(f"💡 每个分片将在独立目录中处理，避免文件冲突")
        response = input("确认开始稳定模式？(y/n): ").lower().strip()
        
        if response != 'y':
            print("❌ 用户取消了稳定模式")
            return
        
        self.start_time = time.time()
        
        # 启动稳定并发处理
        print(f"\n🚀 启动 {self.max_browsers} 个浏览器实例...")
        
        with ThreadPoolExecutor(max_workers=self.max_browsers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self.process_single_shard, task): task 
                for task in tasks
            }
            
            # 收集结果
            completed_count = 0
            failed_count = 0
            
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                
                try:
                    result = future.result()
                    self.results[task['task_id']] = result
                    
                    if result['status'] == 'completed':
                        completed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"❌ [{task['task_id']}] 线程执行异常: {e}")
                    self.results[task['task_id']] = {
                        'task_id': task['task_id'],
                        'status': 'failed',
                        'error': f"线程执行异常: {e}",
                        'duration': 0
                    }
                    failed_count += 1
        
        # 清理工作目录
        try:
            if os.path.exists(self.work_dir):
                shutil.rmtree(self.work_dir)
        except:
            pass
        
        # 输出最终报告
        self.print_final_report(completed_count, failed_count)
    
    def print_final_report(self, completed_count: int, failed_count: int):
        """打印最终报告"""
        end_time = time.time()
        total_duration = end_time - self.start_time
        
        print("\n" + "🎉" * 60)
        print("🏁 稳定版8浏览器处理完成！")
        print("🎉" * 60)
        
        print(f"\n📊 最终统计:")
        print(f"  ✅ 成功: {completed_count} 个分片")
        print(f"  ❌ 失败: {failed_count} 个分片")
        print(f"  ⏱️  总耗时: {total_duration/3600:.2f} 小时")
        print(f"  🚀 平均速度: {completed_count/(total_duration/3600):.1f} 分片/小时")
        
        if self.results:
            print(f"\n📋 详细结果:")
            for task_id, result in sorted(self.results.items()):
                status_icon = "✅" if result['status'] == 'completed' else "❌"
                duration_str = f"{result['duration']/60:.1f}min" if result['duration'] > 0 else "0min"
                
                print(f"  {status_icon} {task_id:<25} - {result['status']:<10} ({duration_str})")
                
                if result['status'] == 'completed' and 'output_file' in result:
                    print(f"      输出: {result['output_file']}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='稳定版8浏览器并发处理器')
    parser.add_argument('--browsers', type=int, default=8, help='浏览器数量（默认8个）')
    
    args = parser.parse_args()
    
    print("🎮 欢迎使用稳定版8浏览器并发处理器！")
    print("💡 专为下班摸鱼设计，解决文件冲突问题！")
    print("=" * 60)
    
    # 创建处理器
    processor = Stable8BrowserProcessor(max_browsers=args.browsers)
    
    # 开始稳定处理
    try:
        processor.run_stable_processing()
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断了稳定模式")
        print(f"   已完成的分片数据已保存")
    except Exception as e:
        print(f"\n❌ 稳定模式出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
