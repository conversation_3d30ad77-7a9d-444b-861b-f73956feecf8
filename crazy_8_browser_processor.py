"""
疯狂8浏览器并发处理器 🚀
专门为下班摸鱼设计的分片数据处理器，支持8个浏览器同时跑！

特性：
- 8个Chrome浏览器实例同时工作
- 自动读取分片数据文件
- 智能任务分配和负载均衡
- 实时进度显示和性能监控
- 支持暂停/恢复功能
"""

import os
import sys
import time
import threading
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from integrated_restaurant_processor import process_restaurants_with_browser
import glob
from typing import List, Dict


class Crazy8BrowserProcessor:
    """疯狂8浏览器并发处理器"""
    
    def __init__(self, max_browsers=10):
        self.max_browsers = max_browsers
        self.results = {}
        self.lock = threading.Lock()
        self.start_time = None
        self.processed_count = 0
        self.total_tasks = 0
        
        print(f"🚀 疯狂8浏览器并发处理器启动！")
        print(f"⚡ 浏览器数量: {max_browsers}")
        print(f"💻 准备开始疯狂处理...")
    
    def find_shard_files(self) -> Dict[str, List[str]]:
        """查找所有分片文件"""
        shard_files = {}
        
        # 查找各国家的分片数据目录
        countries = ["印度尼西亚", "菲律宾", "马来西亚"]
        
        for country in countries:
            shard_dir = f"{country}_分片数据"
            if os.path.exists(shard_dir):
                pattern = os.path.join(shard_dir, f"{country}_restaurant_urls_第*份.csv")
                files = glob.glob(pattern)
                if files:
                    # 按文件名排序
                    files.sort(key=lambda x: int(x.split('第')[1].split('份')[0]))
                    shard_files[country] = files
                    print(f"📁 发现 {country}: {len(files)} 个分片文件")
        
        return shard_files
    
    def create_processing_tasks(self, shard_files: Dict[str, List[str]]) -> List[Dict]:
        """创建处理任务列表"""
        tasks = []
        
        for country, files in shard_files.items():
            for file_path in files:
                # 提取分片编号
                shard_num = file_path.split('第')[1].split('份')[0]
                
                task = {
                    'country': country,
                    'shard_file': file_path,
                    'shard_num': shard_num,
                    'task_id': f"{country}_第{shard_num}份",
                    'output_file': f"{country}_restaurant_details_第{shard_num}份_translated.csv"
                }
                tasks.append(task)
        
        return tasks
    
    def process_single_shard(self, task: Dict) -> Dict:
        """处理单个分片文件"""
        task_id = task['task_id']
        country = task['country']
        shard_file = task['shard_file']
        
        start_time = time.time()
        
        print(f"🌍 [浏览器{threading.current_thread().ident % 10}] 开始处理: {task_id}")
        
        try:
            # 临时替换原始URL文件，让处理器使用分片文件
            original_file = f"{country}_restaurant_urls.csv"
            backup_file = f"{country}_restaurant_urls_backup_{int(time.time())}.csv"
            
            # 备份原文件
            if os.path.exists(original_file):
                os.rename(original_file, backup_file)
            
            # 复制分片文件到原文件位置
            import shutil
            shutil.copy2(shard_file, original_file)
            
            # 创建独立的输出文件名
            shard_num = task['shard_num']
            original_output = f"{country}_restaurant_details_translated.csv"
            shard_output = f"{country}_restaurant_details_第{shard_num}份_translated.csv"
            
            # 如果输出文件已存在，先备份
            if os.path.exists(original_output):
                temp_backup = f"{original_output}_temp_{int(time.time())}"
                os.rename(original_output, temp_backup)
            
            # 调用处理函数
            process_restaurants_with_browser(country)
            
            # 重命名输出文件
            if os.path.exists(original_output):
                os.rename(original_output, shard_output)
                print(f"✅ [{task_id}] 输出文件: {shard_output}")
            
            # 恢复原文件
            if os.path.exists(backup_file):
                os.rename(backup_file, original_file)
            
            # 恢复原输出文件
            temp_files = glob.glob(f"{original_output}_temp_*")
            if temp_files:
                os.rename(temp_files[0], original_output)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 更新统计
            with self.lock:
                self.processed_count += 1
                progress = (self.processed_count / self.total_tasks) * 100
                print(f"🎉 [{task_id}] 完成！耗时: {duration/60:.1f}分钟 | 总进度: {progress:.1f}%")
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'duration': duration,
                'output_file': shard_output,
                'start_time': start_time,
                'end_time': end_time
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ [{task_id}] 处理失败: {e}")
            
            # 清理：恢复原文件
            try:
                backup_files = glob.glob(f"{country}_restaurant_urls_backup_*.csv")
                if backup_files:
                    os.rename(backup_files[0], f"{country}_restaurant_urls.csv")
            except:
                pass
            
            return {
                'task_id': task_id,
                'status': 'failed',
                'error': str(e),
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time
            }
    
    def run_crazy_processing(self):
        """开始疯狂处理！"""
        print("\n" + "🔥" * 60)
        print("🚀 疯狂8浏览器并发处理开始！")
        print("🔥" * 60)
        
        # 查找分片文件
        shard_files = self.find_shard_files()
        if not shard_files:
            print("❌ 没有找到分片文件，请先运行数据拆分脚本")
            return
        
        # 创建任务列表
        tasks = self.create_processing_tasks(shard_files)
        self.total_tasks = len(tasks)
        
        print(f"\n📊 任务统计:")
        for country, files in shard_files.items():
            print(f"  {country}: {len(files)} 个分片")
        print(f"  总任务数: {self.total_tasks}")
        
        # 随机打乱任务顺序，避免同一国家的任务集中
        random.shuffle(tasks)
        
        print(f"\n🤔 准备用 {self.max_browsers} 个浏览器疯狂处理这些任务...")
        response = input("确认开始疯狂模式？(y/n): ").lower().strip()
        
        if response != 'y':
            print("❌ 用户取消了疯狂模式")
            return
        
        self.start_time = time.time()
        
        # 启动疯狂并发处理
        print(f"\n🚀 启动 {self.max_browsers} 个浏览器实例...")
        
        with ThreadPoolExecutor(max_workers=self.max_browsers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self.process_single_shard, task): task 
                for task in tasks
            }
            
            # 收集结果
            completed_count = 0
            failed_count = 0
            
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                
                try:
                    result = future.result()
                    self.results[task['task_id']] = result
                    
                    if result['status'] == 'completed':
                        completed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"❌ [{task['task_id']}] 线程执行异常: {e}")
                    self.results[task['task_id']] = {
                        'task_id': task['task_id'],
                        'status': 'failed',
                        'error': f"线程执行异常: {e}",
                        'duration': 0
                    }
                    failed_count += 1
        
        # 输出最终报告
        self.print_final_report(completed_count, failed_count)
    
    def print_final_report(self, completed_count: int, failed_count: int):
        """打印最终报告"""
        end_time = time.time()
        total_duration = end_time - self.start_time
        
        print("\n" + "🎉" * 60)
        print("🏁 疯狂8浏览器处理完成！")
        print("🎉" * 60)
        
        print(f"\n📊 最终统计:")
        print(f"  ✅ 成功: {completed_count} 个分片")
        print(f"  ❌ 失败: {failed_count} 个分片")
        print(f"  ⏱️  总耗时: {total_duration/3600:.2f} 小时")
        print(f"  🚀 平均速度: {completed_count/(total_duration/3600):.1f} 分片/小时")
        
        if self.results:
            print(f"\n📋 详细结果:")
            for task_id, result in sorted(self.results.items()):
                status_icon = "✅" if result['status'] == 'completed' else "❌"
                duration_str = f"{result['duration']/60:.1f}min" if result['duration'] > 0 else "0min"
                
                print(f"  {status_icon} {task_id:<25} - {result['status']:<10} ({duration_str})")
                
                if result['status'] == 'failed' and 'error' in result:
                    print(f"      错误: {result['error']}")
                elif result['status'] == 'completed' and 'output_file' in result:
                    print(f"      输出: {result['output_file']}")
        
        # 计算效率提升
        if completed_count > 0:
            avg_duration = sum(r['duration'] for r in self.results.values() if r['status'] == 'completed') / completed_count
            sequential_time = avg_duration * len(self.results)
            time_saved = sequential_time - total_duration
            efficiency_gain = (time_saved / sequential_time) * 100 if sequential_time > 0 else 0
            
            print(f"\n⚡ 疯狂模式效率:")
            print(f"  📈 预计串行时间: {sequential_time/3600:.2f} 小时")
            print(f"  🚀 实际并发时间: {total_duration/3600:.2f} 小时")
            print(f"  💾 节省时间: {time_saved/3600:.2f} 小时")
            print(f"  📊 效率提升: {efficiency_gain:.1f}%")
            print(f"  🔥 疯狂指数: {self.max_browsers}x 浏览器并发！")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='疯狂8浏览器并发处理器')
    parser.add_argument('--browsers', type=int, default=8, help='浏览器数量（默认8个）')
    
    args = parser.parse_args()
    
    print("🎮 欢迎使用疯狂8浏览器并发处理器！")
    print("💡 专为下班摸鱼设计，让您的电脑疯狂起来！")
    print("=" * 60)
    
    # 创建处理器
    processor = Crazy8BrowserProcessor(max_browsers=args.browsers)
    
    # 开始疯狂处理
    try:
        processor.run_crazy_processing()
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断了疯狂模式")
        print(f"   已完成的分片数据已保存")
    except Exception as e:
        print(f"\n❌ 疯狂模式出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
