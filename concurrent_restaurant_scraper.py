#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发餐厅详情爬虫执行器 - 支持多进程并发处理多个国家的餐厅详情
"""

import multiprocessing
import time
import os
import sys
from restaurant import scrape_restaurant_details

def run_country_restaurant(country):
    """为单个国家运行餐厅详情爬虫的进程函数"""
    print(f"🚀 启动进程处理国家餐厅详情: {country}")
    start_time = time.time()
    
    try:
        # 检查国家的餐厅URL文件是否存在
        restaurant_urls_file = f"{country}_restaurant_urls.csv"
        if not os.path.exists(restaurant_urls_file):
            print(f"❌ 错误: 找不到餐厅URL文件 '{restaurant_urls_file}'。跳过国家 {country}。")
            return f"{country}: 餐厅URL文件不存在"
        
        # 执行餐厅详情爬虫
        result = scrape_restaurant_details(data={"country": country})
        elapsed = time.time() - start_time
        print(f"✅ 国家 {country} 餐厅详情处理完成，耗时: {elapsed:.2f} 秒")
        return f"{country}: 成功 ({elapsed:.2f}s)"
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 国家 {country} 餐厅详情处理失败: {e}，耗时: {elapsed:.2f} 秒")
        return f"{country}: 失败 - {str(e)}"

def main():
    """主函数 - 并发执行餐厅详情爬虫"""
    print("=" * 60)
    print("🍽️  并发餐厅详情爬虫执行器启动")
    print("=" * 60)
    
    start_time = time.time()
    
    # 定义要并发处理的国家列表
    countries_to_scrape = ["印度尼西亚", "菲律宾", "马来西亚"]
    
    # 检查所有国家的餐厅URL文件是否存在
    available_countries = []
    for country in countries_to_scrape:
        restaurant_urls_file = f"{country}_restaurant_urls.csv"
        if os.path.exists(restaurant_urls_file):
            available_countries.append(country)
            print(f"📁 找到餐厅URL文件: {country}")
        else:
            print(f"⚠️  警告: 找不到餐厅URL文件: {country}，将跳过")
    
    if not available_countries:
        print("❌ 错误: 没有可用的餐厅URL文件，请检查文件是否存在")
        return
    
    print(f"\n🎯 准备并发处理 {len(available_countries)} 个国家的餐厅详情: {', '.join(available_countries)}")
    print(f"🖥️  最大并发数: 3 (浏览器实例)")
    print("-" * 60)
    
    # 创建进程池，最大并发数为3
    with multiprocessing.Pool(processes=3) as pool:
        results = pool.map(run_country_restaurant, available_countries)
    
    # 输出结果汇总
    print("\n" + "=" * 60)
    print("📊 并发处理结果汇总")
    print("=" * 60)
    for result in results:
        print(f"   {result}")
    
    total_time = time.time() - start_time
    print(f"\n⏱️  总耗时: {total_time:.2f} 秒")
    print("🎉 所有国家餐厅详情并发处理完毕！")

if __name__ == "__main__":
    # 确保在Windows上正确运行多进程
    if sys.platform.startswith('win'):
        multiprocessing.freeze_support()
    
    main()