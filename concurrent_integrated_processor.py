"""
并发餐厅数据智能处理器
支持多国家并发处理，每个国家使用独立的浏览器实例
"""

import os
import sys
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from integrated_restaurant_processor import IntegratedRestaurantProcessor, process_restaurants_with_browser


class ConcurrentIntegratedProcessor:
    """并发餐厅数据智能处理器"""
    
    def __init__(self, max_workers=3):
        self.max_workers = max_workers
        self.results = {}
        self.lock = threading.Lock()
    
    def process_single_country(self, country: str, **kwargs) -> dict:
        """处理单个国家的餐厅数据"""
        start_time = time.time()
        
        print(f"\n🌍 [线程] 开始处理国家: {country}")
        
        try:
            # 检查餐厅URL文件是否存在
            restaurant_urls_file = f"{country}_restaurant_urls.csv"
            if not os.path.exists(restaurant_urls_file):
                error_msg = f"找不到餐厅URL文件: {restaurant_urls_file}"
                print(f"❌ [{country}] {error_msg}")
                return {
                    'country': country,
                    'status': 'failed',
                    'error': error_msg,
                    'duration': time.time() - start_time
                }
            
            # 调用处理函数
            process_restaurants_with_browser(country)
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ [{country}] 处理完成，耗时: {duration/3600:.2f} 小时")
            
            return {
                'country': country,
                'status': 'completed',
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ [{country}] 处理失败: {e}")
            
            return {
                'country': country,
                'status': 'failed',
                'error': str(e),
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time
            }
    
    def process_countries_concurrent(self, countries: list, **kwargs):
        """并发处理多个国家"""
        print(f"🚀 开始并发处理 {len(countries)} 个国家")
        print(f"⚡ 最大并发数: {self.max_workers}")
        print(f"📍 国家列表: {', '.join(countries)}")
        print("=" * 80)
        
        start_time = time.time()
        
        # 使用线程池执行并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_country = {
                executor.submit(self.process_single_country, country, **kwargs): country 
                for country in countries
            }
            
            # 收集结果
            completed_count = 0
            failed_count = 0
            
            for future in as_completed(future_to_country):
                country = future_to_country[future]
                
                try:
                    result = future.result()
                    self.results[country] = result
                    
                    if result['status'] == 'completed':
                        completed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"❌ [{country}] 线程执行异常: {e}")
                    self.results[country] = {
                        'country': country,
                        'status': 'failed',
                        'error': f"线程执行异常: {e}",
                        'duration': 0
                    }
                    failed_count += 1
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 输出总结报告
        self.print_summary_report(total_duration, completed_count, failed_count)
    
    def print_summary_report(self, total_duration: float, completed_count: int, failed_count: int):
        """打印总结报告"""
        print("\n" + "=" * 80)
        print("🎉 并发处理完成！")
        print("=" * 80)
        
        print(f"📊 处理统计:")
        print(f"  ✅ 成功: {completed_count} 个国家")
        print(f"  ❌ 失败: {failed_count} 个国家")
        print(f"  ⏱️  总耗时: {total_duration/3600:.2f} 小时")
        
        if self.results:
            print(f"\n📋 详细结果:")
            for country, result in self.results.items():
                status_icon = "✅" if result['status'] == 'completed' else "❌"
                duration_str = f"{result['duration']/3600:.2f}h" if result['duration'] > 0 else "0h"
                
                print(f"  {status_icon} {country:<12} - {result['status']:<10} ({duration_str})")
                
                if result['status'] == 'failed' and 'error' in result:
                    print(f"      错误: {result['error']}")
        
        # 计算效率提升
        if completed_count > 0:
            avg_duration = sum(r['duration'] for r in self.results.values() if r['status'] == 'completed') / completed_count
            sequential_time = avg_duration * len(self.results)
            time_saved = sequential_time - total_duration
            efficiency_gain = (time_saved / sequential_time) * 100 if sequential_time > 0 else 0
            
            print(f"\n⚡ 并发效率:")
            print(f"  📈 预计串行时间: {sequential_time/3600:.2f} 小时")
            print(f"  🚀 实际并发时间: {total_duration/3600:.2f} 小时")
            print(f"  💾 节省时间: {time_saved/3600:.2f} 小时")
            print(f"  📊 效率提升: {efficiency_gain:.1f}%")


def check_prerequisites(countries: list) -> tuple:
    """检查运行前提条件"""
    print("🔍 检查运行环境...")
    
    available_countries = []
    missing_countries = []
    
    for country in countries:
        urls_file = f"{country}_restaurant_urls.csv"
        if os.path.exists(urls_file):
            available_countries.append(country)
            print(f"  ✅ {country}: {urls_file}")
        else:
            missing_countries.append(country)
            print(f"  ❌ {country}: 缺少 {urls_file}")
    
    # 检查Chrome浏览器
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    if os.path.exists(chrome_path):
        print(f"  ✅ Chrome浏览器: {chrome_path}")
    else:
        print(f"  ❌ Chrome浏览器: 找不到 {chrome_path}")
        return [], countries
    
    return available_countries, missing_countries


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='并发餐厅数据智能处理器')
    parser.add_argument('--country', type=str, default='菲律宾', help='要处理的国家（支持逗号分隔）')
    parser.add_argument('--countries', type=str, help='要处理的多个国家，用逗号分隔')
    parser.add_argument('--max-workers', type=int, default=3, help='最大并发数（默认3）')
    parser.add_argument('--batch-size', type=int, default=10, help='翻译批次大小')
    parser.add_argument('--delay', type=int, default=20, help='请求间隔时间（秒）')
    parser.add_argument('--api-url', type=str, help='翻译API地址')
    parser.add_argument('--model-name', type=str, help='翻译模型名称')
    
    args = parser.parse_args()
    
    # 确定要处理的国家列表
    if args.countries:
        countries = [c.strip() for c in args.countries.split(',')]
    elif ',' in args.country:
        countries = [c.strip() for c in args.country.split(',')]
    else:
        countries = [args.country]
    
    print(f"🎯 并发餐厅数据智能处理器")
    print(f"📍 目标国家: {', '.join(countries)}")
    print(f"⚡ 最大并发数: {args.max_workers}")
    print("=" * 80)
    
    # 检查前提条件
    available_countries, missing_countries = check_prerequisites(countries)
    
    if missing_countries:
        print(f"\n⚠️ 以下国家缺少必要文件:")
        for country in missing_countries:
            print(f"  - {country}")
        print(f"\n💡 请先运行以下命令生成餐厅URL文件:")
        print(f"   python scrape_restaurant_urls.py")
    
    if not available_countries:
        print(f"\n❌ 没有可处理的国家，程序退出")
        return
    
    if len(available_countries) < len(countries):
        print(f"\n🤔 只能处理 {len(available_countries)} 个国家，是否继续？")
        print(f"   可处理: {', '.join(available_countries)}")
        response = input("   继续？(y/n): ").lower().strip()
        if response != 'y':
            print("❌ 用户取消处理")
            return
    
    # 创建并发处理器
    processor = ConcurrentIntegratedProcessor(max_workers=args.max_workers)
    
    # 开始并发处理
    try:
        processor.process_countries_concurrent(
            available_countries,
            batch_size=args.batch_size,
            requests_per_minute=60 / args.delay if args.delay else 3,
            api_url=args.api_url,
            model_name=args.model_name
        )
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断处理")
        print(f"   已完成的国家数据已保存")
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
