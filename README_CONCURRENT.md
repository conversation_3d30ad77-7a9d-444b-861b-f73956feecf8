# 并发爬虫方案说明

## 📋 方案概述

基于现有的 `scrape_restaurant_urls.py` 爬虫，我设计了两种并发方案来解决顺序处理的性能瓶颈问题。

## 🎯 当前问题

- 原代码顺序处理国家列表，一次只能处理一个国家
- 每个国家处理时间较长，总体效率低下
- 需要并发处理多个国家以提升效率

## 🚀 并发方案

### 方案一：多进程并发 (`concurrent_scraper.py`)

**特点：**
- 使用 `multiprocessing.Pool` 实现
- 每个国家在独立进程中运行
- 完全隔离的浏览器实例和内存空间
- 稳定性高，适合长时间运行

**优势：**
- 真正的并行执行
- 进程崩溃不影响其他进程
- 充分利用多核CPU

**使用方法：**
```bash
python concurrent_scraper.py
```

### 方案二：多线程并发 (`threaded_scraper.py`)

**特点：**
- 使用 `concurrent.futures.ThreadPoolExecutor` 实现
- 每个国家在独立线程中运行
- 资源消耗更少，启动更快

**优势：**
- 轻量级并发
- 线程间切换开销小
- 适合I/O密集型任务

**使用方法：**
```bash
python threaded_scraper.py
```

## 🔧 技术实现细节

### 数据一致性保障
1. **文件隔离**：每个国家有独立的CSV文件
   - `{国家}_city_urls.csv` - 主控文件
   - `{国家}_restaurant_urls.csv` - 餐厅URL文件
   - `{国家}_pending_pages.csv` - 待处理页面文件

2. **去重机制**：原有的 `load_existing_restaurant_urls` 函数确保URL不重复

3. **状态管理**：`update_master_status` 函数确保任务状态正确更新

### 并发控制
- 最大并发数：3个浏览器实例
- 自动检测可用国家文件
- 详细的进度和错误日志

## 📊 性能预期

| 方案 | 预期速度提升 | 资源消耗 | 稳定性 |
|------|-------------|----------|--------|
| 多进程 | 3倍 | 较高 | 高 |
| 多线程 | 2.5-3倍 | 较低 | 中等 |

## 🛠️ 使用步骤

1. **准备国家文件**：确保所有要处理的国家都有对应的 `{国家}_city_urls.csv` 文件

2. **选择并发方案**：
   - 推荐使用多进程方案 (`concurrent_scraper.py`)
   - 如果资源有限，使用多线程方案 (`threaded_scraper.py`)

3. **修改国家列表**：在脚本中修改 `countries_to_scrape` 列表

4. **运行并发爬虫**：
   ```bash
   # 多进程方案
   python concurrent_scraper.py
   
   # 多线程方案  
   python threaded_scraper.py
   ```

## ⚠️ 注意事项

1. **系统资源**：并发运行会占用更多内存和CPU资源
2. **网络带宽**：确保网络连接稳定，避免因网络问题导致失败
3. **文件权限**：确保有足够的文件写入权限
4. **错误处理**：单个国家的失败不会影响其他国家的处理

## 📈 监控和调试

- 每个进程/线程都有独立的日志输出
- 实时显示处理进度和耗时
- 详细的错误信息和异常处理
- 最终的结果汇总报告

## 🔄 扩展性

可以轻松调整并发数量：
```python
# 修改并发进程数
with multiprocessing.Pool(processes=5) as pool:  # 改为5个并发

# 修改并发线程数  
with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:  # 改为5个并发
```

## 🎉 预期效果

通过并发处理，预计可以将总体爬取时间从原来的顺序处理时间缩短到原来的 1/3 左右，大幅提升爬取效率。