"""
餐厅数据智能处理器 - 运行脚本
简化的运行脚本，用于快速启动餐厅数据处理任务
"""

import os
import sys
from integrated_restaurant_processor import IntegratedRestaurantProcessor


def check_prerequisites(country: str) -> bool:
    """检查运行前提条件"""
    print(f"🔍 检查 {country} 的运行环境...")
    
    # 检查餐厅URL文件
    urls_file = f"{country}_restaurant_urls.csv"
    if not os.path.exists(urls_file):
        print(f"❌ 缺少餐厅URL文件: {urls_file}")
        print(f"   请先运行 scrape_restaurant_urls.py 生成此文件")
        return False
    
    # 检查Chrome浏览器
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    if not os.path.exists(chrome_path):
        print(f"❌ 找不到Chrome浏览器: {chrome_path}")
        print(f"   请确保Chrome浏览器已正确安装")
        return False
    
    print(f"✅ 环境检查通过")
    return True


def show_progress_info(country: str):
    """显示处理进度信息"""
    urls_file = f"{country}_restaurant_urls.csv"
    checkpoint_file = f"{country}_integrated_checkpoint.txt"
    output_file = f"{country}_restaurant_details_translated.csv"
    cache_file = f"{country}_translation_cache.json"
    
    print(f"\n📊 {country} 处理进度信息:")
    
    # 总URL数量
    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            total_urls = sum(1 for line in f) - 1  # 减去标题行
        print(f"  📂 总餐厅数量: {total_urls}")
    except:
        print(f"  📂 总餐厅数量: 无法读取")
    
    # 当前进度
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                current_index = int(f.read().strip())
            print(f"  📍 当前进度: {current_index}/{total_urls} ({current_index/total_urls*100:.1f}%)")
        except:
            print(f"  📍 当前进度: 无法读取断点")
    else:
        print(f"  📍 当前进度: 0/{total_urls} (0.0%)")
    
    # 输出文件状态
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                processed_count = sum(1 for line in f) - 1  # 减去标题行
            print(f"  📄 已处理记录: {processed_count}")
        except:
            print(f"  📄 已处理记录: 无法读取")
    else:
        print(f"  📄 已处理记录: 0")
    
    # 翻译缓存状态
    if os.path.exists(cache_file):
        try:
            import json
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            print(f"  💾 翻译缓存: {len(cache_data)} 条")
        except:
            print(f"  💾 翻译缓存: 无法读取")
    else:
        print(f"  💾 翻译缓存: 0 条")


def run_single_country(country: str, **kwargs):
    """运行单个国家的处理任务"""
    print(f"\n{'='*60}")
    print(f"🌍 开始处理国家: {country}")
    print(f"{'='*60}")
    
    # 检查前提条件
    if not check_prerequisites(country):
        return False
    
    # 显示进度信息
    show_progress_info(country)
    
    # 确认是否继续
    print(f"\n🤔 是否开始处理 {country} 的餐厅数据？")
    print(f"   这将启动浏览器并开始爬取和翻译过程...")
    response = input("   继续？(y/n): ").lower().strip()
    
    if response != 'y':
        print(f"❌ 用户取消了 {country} 的处理任务")
        return False
    
    try:
        # 创建处理器实例
        processor = IntegratedRestaurantProcessor(
            country=country,
            batch_size=kwargs.get('batch_size', 10),
            requests_per_minute=kwargs.get('requests_per_minute', 3),
            api_url=kwargs.get('api_url'),
            model_name=kwargs.get('model_name')
        )
        
        # 开始处理
        print(f"\n🚀 开始处理 {country}...")
        processor.process_restaurants()
        
        print(f"✅ {country} 处理完成！")
        
        # 显示最终结果
        show_progress_info(country)
        return True
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断了 {country} 的处理任务")
        print(f"   进度已保存，可以稍后继续")
        return False
    except Exception as e:
        print(f"❌ {country} 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 餐厅数据智能处理器")
    print("=" * 60)
    print("功能特性:")
    print("  ✨ 在爬取间隔期间自动处理翻译任务")
    print("  ✨ 智能缓存机制，避免重复翻译")
    print("  ✨ 完整的断点续传功能")
    print("  ✨ 实时翻译状态跟踪")
    print("=" * 60)
    
    # 支持的国家列表
    available_countries = []
    for country in ["印度尼西亚", "菲律宾", "马来西亚"]:
        urls_file = f"{country}_restaurant_urls.csv"
        if os.path.exists(urls_file):
            available_countries.append(country)
    
    if not available_countries:
        print("❌ 没有找到可处理的国家数据文件")
        print("   请先运行 scrape_restaurant_urls.py 生成餐厅URL文件")
        return
    
    print(f"\n📍 可处理的国家:")
    for i, country in enumerate(available_countries, 1):
        print(f"  {i}. {country}")
    
    # 选择处理方式
    print(f"\n🎯 请选择处理方式:")
    print(f"  1. 处理单个国家")
    print(f"  2. 处理所有国家")
    print(f"  3. 查看进度信息")
    print(f"  4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # 单个国家处理
            print(f"\n请选择要处理的国家:")
            for i, country in enumerate(available_countries, 1):
                print(f"  {i}. {country}")
            
            country_choice = input(f"请输入国家编号 (1-{len(available_countries)}): ").strip()
            try:
                country_index = int(country_choice) - 1
                if 0 <= country_index < len(available_countries):
                    country = available_countries[country_index]
                    run_single_country(country)
                else:
                    print("❌ 无效的国家编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == "2":
            # 所有国家处理
            print(f"\n🌍 将依次处理所有国家: {', '.join(available_countries)}")
            confirm = input("确认继续？(y/n): ").lower().strip()
            
            if confirm == 'y':
                success_count = 0
                for country in available_countries:
                    if run_single_country(country):
                        success_count += 1
                
                print(f"\n🎉 批量处理完成！")
                print(f"   成功: {success_count}/{len(available_countries)} 个国家")
            else:
                print("❌ 用户取消了批量处理")
        
        elif choice == "3":
            # 查看进度信息
            for country in available_countries:
                show_progress_info(country)
        
        elif choice == "4":
            print("👋 再见！")
        
        else:
            print("❌ 无效的选择")
    
    except KeyboardInterrupt:
        print("\n👋 用户退出")
    except Exception as e:
        print(f"❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
