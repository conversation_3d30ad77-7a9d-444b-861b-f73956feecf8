"""
餐厅URL数据拆分工具
将大的餐厅URL文件拆分成多个小文件，支持多人协作处理

功能特性：
- 每个小文件包含2000条数据
- 自动创建国家子目录
- 保持原始文件格式完全兼容
- 支持断点续传和重复运行
"""

import os
import csv
import math
from typing import List, Dict


class RestaurantURLSplitter:
    """餐厅URL数据拆分器"""
    
    def __init__(self, chunk_size: int = 2000):
        self.chunk_size = chunk_size
        self.supported_countries = ["印度尼西亚", "菲律宾", "马来西亚"]
    
    def check_source_files(self) -> Dict[str, dict]:
        """检查源文件状态"""
        file_info = {}
        
        print("🔍 检查源文件状态...")
        
        for country in self.supported_countries:
            source_file = f"{country}_restaurant_urls.csv"
            
            if os.path.exists(source_file):
                try:
                    with open(source_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                        total_rows = len(rows) - 1  # 减去标题行
                        
                    file_info[country] = {
                        'exists': True,
                        'total_rows': total_rows,
                        'total_chunks': math.ceil(total_rows / self.chunk_size),
                        'source_file': source_file
                    }
                    
                    print(f"  ✅ {country}: {total_rows:,} 条数据 → {file_info[country]['total_chunks']} 个分片")
                    
                except Exception as e:
                    file_info[country] = {
                        'exists': False,
                        'error': f"读取失败: {e}"
                    }
                    print(f"  ❌ {country}: 读取失败 - {e}")
            else:
                file_info[country] = {
                    'exists': False,
                    'error': "文件不存在"
                }
                print(f"  ❌ {country}: 文件不存在 - {source_file}")
        
        return file_info
    
    def create_country_directory(self, country: str) -> str:
        """创建国家子目录"""
        dir_name = f"{country}_分片数据"
        
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name}")
        else:
            print(f"📁 目录已存在: {dir_name}")
        
        return dir_name
    
    def split_country_data(self, country: str, file_info: dict) -> bool:
        """拆分单个国家的数据"""
        if not file_info['exists']:
            print(f"❌ 跳过 {country}: {file_info['error']}")
            return False
        
        source_file = file_info['source_file']
        total_chunks = file_info['total_chunks']
        
        print(f"\n🔄 开始拆分 {country} 数据...")
        print(f"   源文件: {source_file}")
        print(f"   总数据: {file_info['total_rows']:,} 条")
        print(f"   分片数: {total_chunks} 个")
        
        # 创建国家目录
        country_dir = self.create_country_directory(country)
        
        try:
            # 读取源文件
            with open(source_file, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                header = next(reader)  # 读取标题行
                all_rows = list(reader)
            
            # 拆分数据
            for chunk_index in range(total_chunks):
                start_idx = chunk_index * self.chunk_size
                end_idx = min(start_idx + self.chunk_size, len(all_rows))
                chunk_rows = all_rows[start_idx:end_idx]
                
                # 生成分片文件名
                chunk_filename = f"{country}_restaurant_urls_第{chunk_index + 1}份.csv"
                chunk_filepath = os.path.join(country_dir, chunk_filename)
                
                # 写入分片文件
                with open(chunk_filepath, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(header)  # 写入标题行
                    writer.writerows(chunk_rows)
                
                actual_rows = len(chunk_rows)
                print(f"  ✅ 第{chunk_index + 1}份: {actual_rows:,} 条数据 → {chunk_filename}")
            
            print(f"✅ {country} 拆分完成！")
            return True
            
        except Exception as e:
            print(f"❌ {country} 拆分失败: {e}")
            return False
    
    def generate_processing_guide(self, file_info: Dict[str, dict]):
        """生成处理指南文件"""
        guide_content = """# 餐厅数据协作处理指南

## 📋 数据分片概览

"""
        
        total_files = 0
        total_data = 0
        
        for country, info in file_info.items():
            if info['exists']:
                guide_content += f"### {country}\n"
                guide_content += f"- **总数据量**: {info['total_rows']:,} 条\n"
                guide_content += f"- **分片数量**: {info['total_chunks']} 个文件\n"
                guide_content += f"- **每片大小**: 约 {self.chunk_size:,} 条数据\n"
                guide_content += f"- **文件位置**: `{country}_分片数据/`\n\n"
                
                total_files += info['total_chunks']
                total_data += info['total_rows']
        
        guide_content += f"""## 🎯 协作分工建议

**总计**: {total_data:,} 条数据，{total_files} 个分片文件

**推荐分工方案**（7-8人团队）：
- **人员1-2**: 处理菲律宾数据（约{file_info.get('菲律宾', {}).get('total_chunks', 0)}个分片）
- **人员3-4**: 处理马来西亚数据（约{file_info.get('马来西亚', {}).get('total_chunks', 0)}个分片）
- **人员5-8**: 处理印度尼西亚数据（约{file_info.get('印度尼西亚', {}).get('total_chunks', 0)}个分片）

## 🚀 处理步骤

### 1. 选择分片文件
每人选择一个或多个分片文件进行处理，避免重复。

### 2. 处理命令
```bash
# 使用整合处理器处理单个分片
python integrated_restaurant_processor.py --country [国家名]

# 或使用传统方式
python restaurant.py
python translator.py
```

### 3. 文件命名规范
处理完成后的文件命名：
- `{country}_restaurant_details_第{i}份.csv` - 原始餐厅详情
- `{country}_restaurant_details_translated_第{i}份.csv` - 翻译后数据

### 4. 结果合并
所有分片处理完成后，使用合并脚本：
```bash
python merge_restaurant_results.py
```

## ⚠️ 注意事项

1. **避免重复处理**: 请在团队中协调，确保每个分片只被一人处理
2. **保持文件格式**: 不要修改CSV文件的列结构
3. **备份重要数据**: 处理前请备份原始文件
4. **及时同步进度**: 定期更新处理进度，避免冲突

## 📊 预估处理时间

- **单个分片**: 约2-4小时（包含翻译）
- **总体时间**: 并发处理可将总时间缩短至1-2天
- **建议**: 每人同时处理1-2个分片，避免系统资源冲突

## 🔧 故障排除

如遇到问题，请检查：
1. Chrome浏览器是否正确安装
2. 网络连接是否稳定
3. API调用是否正常
4. 磁盘空间是否充足

---
生成时间: """ + time.strftime('%Y-%m-%d %H:%M:%S') + """
"""
"""
        
        # 保存指南文件
        with open("餐厅数据协作处理指南.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"📖 生成处理指南: 餐厅数据协作处理指南.md")
    
    def run_split(self, countries: List[str] = None):
        """执行拆分任务"""
        if countries is None:
            countries = self.supported_countries
        
        print("🎯 餐厅URL数据拆分工具")
        print("=" * 60)
        print(f"📦 分片大小: {self.chunk_size:,} 条数据/文件")
        print(f"🌍 目标国家: {', '.join(countries)}")
        print("=" * 60)
        
        # 检查源文件
        file_info = self.check_source_files()
        
        # 过滤可用的国家
        available_countries = [c for c in countries if file_info.get(c, {}).get('exists', False)]
        
        if not available_countries:
            print("\n❌ 没有可处理的国家数据文件")
            return
        
        print(f"\n✅ 可处理的国家: {', '.join(available_countries)}")
        
        # 确认是否继续
        response = input(f"\n🤔 是否开始拆分数据？(y/n): ").lower().strip()
        if response != 'y':
            print("❌ 用户取消拆分")
            return
        
        # 执行拆分
        success_count = 0
        for country in available_countries:
            if self.split_country_data(country, file_info[country]):
                success_count += 1
        
        # 生成处理指南
        if success_count > 0:
            self.generate_processing_guide(file_info)
        
        # 输出总结
        print(f"\n" + "=" * 60)
        print(f"🎉 拆分任务完成！")
        print(f"✅ 成功: {success_count}/{len(available_countries)} 个国家")
        print(f"📁 分片文件已保存到各国家子目录中")
        print(f"📖 请查看 '餐厅数据协作处理指南.md' 了解使用方法")
        print("=" * 60)


def main():
    """主函数"""
    import argparse
    import time
    
    parser = argparse.ArgumentParser(description='餐厅URL数据拆分工具')
    parser.add_argument('--chunk-size', type=int, default=2000, help='每个分片的数据量（默认2000）')
    parser.add_argument('--countries', type=str, help='要拆分的国家，用逗号分隔')
    
    args = parser.parse_args()
    
    # 确定要处理的国家
    if args.countries:
        countries = [c.strip() for c in args.countries.split(',')]
    else:
        countries = None  # 使用默认的所有国家
    
    # 创建拆分器并执行
    splitter = RestaurantURLSplitter(chunk_size=args.chunk_size)
    splitter.run_split(countries)


if __name__ == "__main__":
    main()
