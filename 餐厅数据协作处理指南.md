# 餐厅数据协作处理指南

## 📋 数据分片概览

### 印度尼西亚
- **总数据量**: 22,426 条
- **分片数量**: 12 个文件
- **每片大小**: 约 2,000 条数据
- **文件位置**: `印度尼西亚_分片数据/`

### 菲律宾
- **总数据量**: 14,543 条
- **分片数量**: 8 个文件
- **每片大小**: 约 2,000 条数据
- **文件位置**: `菲律宾_分片数据/`

### 马来西亚
- **总数据量**: 15,466 条
- **分片数量**: 8 个文件
- **每片大小**: 约 2,000 条数据
- **文件位置**: `马来西亚_分片数据/`

## 🎯 协作分工建议

**总计**: 52,435 条数据，28 个分片文件

**推荐分工方案**（7-8人团队）：
- **人员1-2**: 处理菲律宾数据（8个分片）
- **人员3-4**: 处理马来西亚数据（8个分片）
- **人员5-8**: 处理印度尼西亚数据（12个分片）

## 🚀 处理步骤

### 1. 选择分片文件
每人选择一个或多个分片文件进行处理，避免重复。

### 2. 处理命令
```bash
# 使用整合处理器处理单个分片
python integrated_restaurant_processor.py --country [国家名]

# 或使用传统方式
python restaurant.py
python translator.py
```

### 3. 文件命名规范
处理完成后的文件命名：
- `{country}_restaurant_details_第{i}份.csv` - 原始餐厅详情
- `{country}_restaurant_details_translated_第{i}份.csv` - 翻译后数据

### 4. 结果合并
所有分片处理完成后，使用合并脚本：
```bash
python merge_restaurant_results.py
```

## ⚠️ 注意事项

1. **避免重复处理**: 请在团队中协调，确保每个分片只被一人处理
2. **保持文件格式**: 不要修改CSV文件的列结构
3. **备份重要数据**: 处理前请备份原始文件
4. **及时同步进度**: 定期更新处理进度，避免冲突

## 📊 预估处理时间

- **单个分片**: 约2-4小时（包含翻译）
- **总体时间**: 并发处理可将总时间缩短至1-2天
- **建议**: 每人同时处理1-2个分片，避免系统资源冲突

## 🔧 故障排除

如遇到问题，请检查：
1. Chrome浏览器是否正确安装
2. 网络连接是否稳定
3. API调用是否正常
4. 磁盘空间是否充足

## 📁 分片文件结构

```
├── 印度尼西亚_分片数据/
│   ├── 印度尼西亚_restaurant_urls_第1份.csv (2,000条)
│   ├── 印度尼西亚_restaurant_urls_第2份.csv (2,000条)
│   ├── ...
│   └── 印度尼西亚_restaurant_urls_第12份.csv (426条)
├── 菲律宾_分片数据/
│   ├── 菲律宾_restaurant_urls_第1份.csv (2,000条)
│   ├── 菲律宾_restaurant_urls_第2份.csv (2,000条)
│   ├── ...
│   └── 菲律宾_restaurant_urls_第8份.csv (543条)
└── 马来西亚_分片数据/
    ├── 马来西亚_restaurant_urls_第1份.csv (2,000条)
    ├── 马来西亚_restaurant_urls_第2份.csv (2,000条)
    ├── ...
    └── 马来西亚_restaurant_urls_第8份.csv (1,466条)
```

## 🎉 协作优势

- **并发处理**: 7-8人同时工作，处理速度提升7-8倍
- **风险分散**: 单个分片出错不影响其他分片
- **灵活分配**: 可根据个人时间灵活选择分片数量
- **进度可控**: 每个分片独立，便于跟踪进度

---
生成时间: 2025-08-27 23:45:00
