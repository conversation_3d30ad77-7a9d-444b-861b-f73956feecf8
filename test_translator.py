import unittest
from unittest.mock import patch, Mock
import pandas as pd
import json
import sys
import os

# 将 translator.py 所在目录添加到 Python 路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import translator


class TestTranslator(unittest.TestCase):
    
    def setUp(self):
        """测试前的准备工作"""
        # 保存原始配置
        self.original_url = translator.LOCAL_MODEL_API_URL
        self.original_model = translator.LOCAL_MODEL_NAME
        self.original_prompt = translator.prompt_template
        
        # 设置测试用的配置
        translator.LOCAL_MODEL_API_URL = 'http://test-server:1434/api/generate'
        translator.LOCAL_MODEL_NAME = 'test-model'
    
    def tearDown(self):
        """测试后的清理工作"""
        # 恢复原始配置
        translator.LOCAL_MODEL_API_URL = self.original_url
        translator.LOCAL_MODEL_NAME = self.original_model
        translator.prompt_template = self.original_prompt
    
    def test_translate_batch_with_valid_brands(self):
        """测试正常的品牌名称翻译"""
        # 模拟 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "response": "万隆艾德沃|万隆艾德沃|桑托斯将军城快乐蜂|桑托斯将军城快乐蜂|桑托斯将军城J.CO甜甜圈咖啡|古晋生活咖啡@火车露台|古晋佐伊书店咖啡|古晋佐伊书店咖啡"
        }
        
        # 用户提供的测试数据中的品牌名称
        test_brands = [
            'A&W', 'A&W', 'Jollibee', 'Jollibee', 
            'J.CO Donuts & Coffee', 'Life Cafe @ Train Terrace', 
            "Zoey's Books & Coffee", "Zoey's Books & Coffee"
            "Kapau Jaya"
        ]
        
        # 模拟 requests.post 调用
        with patch('translator.requests.post', return_value=mock_response):
            result = translator.translate_batch(test_brands)
            
            # 验证结果
            expected = [
                '万隆艾德沃', '万隆艾德沃', '桑托斯将军城快乐蜂', '桑托斯将军城快乐蜂',
                '桑托斯将军城J.CO甜甜圈咖啡', '古晋生活咖啡@火车露台',
                '古晋佐伊书店咖啡', '古晋佐伊书店咖啡'
            ]
            self.assertEqual(result, expected)
    
    def test_translate_batch_with_empty_brands(self):
        """测试空品牌名称列表"""
        result = translator.translate_batch([])
        self.assertEqual(result, [])
    
    def test_translate_batch_with_none_and_empty_values(self):
        """测试包含 None 和空字符串的品牌列表"""
        test_brands = [None, '', 'A&W', ' ', 'Jollibee']
        
        # 模拟 API 响应（只包含有效品牌的翻译）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "response": "万隆艾德沃|桑托斯将军城快乐蜂"
        }
        
        with patch('translator.requests.post', return_value=mock_response):
            result = translator.translate_batch(test_brands)
            
            # 验证结果：None、空字符串和空格应该返回空字符串
            expected = ['', '', '万隆艾德沃', '', '桑托斯将军城快乐蜂']
            self.assertEqual(result, expected)
    
    def test_translate_batch_api_error(self):
        """测试 API 调用失败的情况"""
        test_brands = ['A&W', 'Jollibee']
        
        # 模拟 API 调用异常
        with patch('translator.requests.post', side_effect=Exception('API连接失败')):
            result = translator.translate_batch(test_brands)
            
            # 验证所有品牌都返回空字符串
            expected = ['', '']
            self.assertEqual(result, expected)
    
    def test_translate_batch_response_format_mismatch(self):
        """测试 API 返回结果数量不匹配的情况"""
        test_brands = ['A&W', 'Jollibee', 'Unknown Brand']
        
        # 模拟 API 返回的翻译结果数量不足
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "response": "万隆艾德沃|桑托斯将军城快乐蜂"  # 只有两个翻译结果
        }
        
        with patch('translator.requests.post', return_value=mock_response):
            result = translator.translate_batch(test_brands)
            
            # 验证结果：前两个有翻译，最后一个为空
            expected = ['万隆艾德沃', '桑托斯将军城快乐蜂', '']
            self.assertEqual(result, expected)
    
    def test_direct_string_mode_with_sample_data(self):
        """测试直接字符串输入模式"""
        # 保存原始配置
        original_use_direct = translator.USE_DIRECT_STRING_INPUT
        original_manual_input = translator.MANUAL_BRANDS_INPUT
        
        # 设置为直接输入模式并提供测试数据
        translator.USE_DIRECT_STRING_INPUT = True
        translator.MANUAL_BRANDS_INPUT = "A&W|Jollibee|J.CO Donuts & Coffee"
        
        # 模拟 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "response": "万隆艾德沃|桑托斯将军城快乐蜂|桑托斯将军城J.CO甜甜圈咖啡"
        }
        
        # 捕获打印输出
        with patch('translator.requests.post', return_value=mock_response):
            # 运行直接输入模式（我们只测试不会产生异常）
            try:
                translator.run_direct_string_mode()
                success = True
            except Exception:
                success = False
            
            self.assertTrue(success)  # 确保函数能正常运行
        
        # 恢复原始配置
        translator.USE_DIRECT_STRING_INPUT = original_use_direct
        translator.MANUAL_BRANDS_INPUT = original_manual_input


if __name__ == '__main__':
    unittest.main()