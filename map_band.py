import pandas as pd
import os


def setup_mappings():
    """
    根据提供的表格和规则，构建分类映射字典。
    返回一个包含所有映射规则的元组。
    """
    # 一级品类代码 -> 名称
    l1_map = {
        'cy01': '中式正餐', 'cy02': '非中式正餐', 'cy04': '西式快餐', 'cy05': '小吃',
        'cy06': '甜品', 'cy07': '烘焙', 'cy08': '酒吧酒馆', 'cy09': '轻饮',
        'cy10': '咖啡馆', 'cy99': '其他'
    }

    # 二级品类代码 -> 名称
    l2_map = {
        'cy0101': '川湘菜', 'cy0102': '江浙沪菜', 'cy0103': '赣湘鄂菜', 'cy0104': '云贵菜',
        'cy0105': '两广菜', 'cy0106': '西北菜', 'cy0107': '东北菜', 'cy0108': '鲁菜',
        'cy0109': '闽菜', 'cy0110': '自助餐', 'cy0111': '火锅',
        'cy0201': '西餐', 'cy0202': '日式料理', 'cy0203': '韩式料理', 'cy0204': '东南亚菜', 'cy0205': '其他国家正餐',
        'cy0401': '汉堡', 'cy0402': '披萨', 'cy0403': '牛扒/烤肉', 'cy0404': '日式', 'cy0405': '韩式',
        'cy0406': '东南亚', 'cy0407': '轻食/代餐',
        'cy0501': '现制点心', 'cy0502': '炸串', 'cy0503': '麻辣烫', 'cy0504': '烧烤', 'cy0505': '蒸品',
        'cy0506': '凉菜', 'cy0507': '预制黔货', 'cy0508': '其他', 'cy0509': '其他风味',
        'cy0601': '冰淇淋', 'cy0602': '糖水', 'cy0603': '奶酪', 'cy0604': '甜品集合店',
        'cy0701': '综合西式烘焙', 'cy0702': '单一品类西式烘焙', 'cy0703': '中式烘焙',
        'cy0801': '酒吧', 'cy0802': '餐吧',
        'cy0901': '现制饮品', 'cy0902': '非现制饮品', 'cy0903': '茶',
        'cy1001': '咖啡馆', 'cy9999': '其他'
    }

    # 关键词 -> 二级品类代码 (关键词越具体，优先级应该越高，所以放在前面)
    keyword_map = {
        '咖啡': 'cy1001', 'Café': 'cy1001', 'Cà Phê': 'cy1001',
        '英国': 'cy0201', '英式': 'cy0201',
        '越南菜': 'cy0204', '亚洲料理': 'cy0204', '泰国菜': 'cy0204', '印度菜': 'cy0204', '东南亚': 'cy0204',
        '火锅': 'cy0111', 'Lẩu': 'cy0111', '川湘菜': 'cy0101', 'Chilên': 'cy0101', '川菜': 'cy0101', '湘菜': 'cy0101',
        '日式料理': 'cy0202', '日料': 'cy0202', '寿司': 'cy0202', '拉面': 'cy0202', '韩式料理': 'cy0203',
        '韩料': 'cy0203', '韩国': 'cy0203',
        '西餐': 'cy0201', '法餐': 'cy0201', '披萨': 'cy0402', '比萨': 'cy0402', 'Pizza': 'cy0402',
        '牛扒': 'cy0403', '烤肉': 'cy0403', 'BBQ': 'cy0403', '炙': 'cy0403', 'Nướng': 'cy0403',
        '烘焙': 'cy0701', '面包': 'cy0701', '蛋糕': 'cy0701', 'Tiệm bánh': 'cy0701', 'Bánh': 'cy0701',
        '酒吧': 'cy0801', '酒馆': 'cy0801', 'Bar': 'cy0801', 'Pub': 'cy0801', 'Beer': 'cy0801',
    }

    # 小吃二级分类关键词
    snack_keyword_map = {
        '麻辣烫': 'cy0503', '酸辣粉': 'cy0509', '米粉': 'cy0509', '螺蛳粉': 'cy0509', '凉皮': 'cy0506',
        '蒸品': 'cy0505', '包子': 'cy0505', 'Hấp': 'cy0505', '炸串': 'cy0502', '炸鸡': 'cy0502',
        '烧烤': 'cy0504', '凉菜': 'cy0506', '点心': 'cy0501', '小笼包': 'cy0501',
    }
    return l1_map, l2_map, keyword_map, snack_keyword_map


def map_brand_category(df, l1_map, l2_map, keyword_map, snack_keyword_map):
    """
    处理DataFrame，添加新的分类列。
    """
    l2_to_l1_code = {l2_code: l1_code for l1_code, l2_code_prefix in
                     {'cy01': 'cy01', 'cy02': 'cy02', 'cy04': 'cy04', 'cy05': 'cy05', 'cy06': 'cy06', 'cy07': 'cy07',
                      'cy08': 'cy08', 'cy09': 'cy09', 'cy10': 'cy10', 'cy99': 'cy99'}.items() for l2_code in l2_map if
                     l2_code.startswith(l2_code_prefix)}

    results = []
    for index, row in df.iterrows():
        l1_category_str = str(row.get('类别', '')).strip()
        l2_category_str = str(row.get('品牌类型', '')).strip()

        # 1. 最高优先级规则：检查是否包含'¥'或品类为空
        if '¥' in l1_category_str or (not l1_category_str and not l2_category_str):
            results.append(('cy99', '其他', 'cy9999', '其他'))
            continue

        found_code = None
        search_target_str = ""

        # 2. 决定使用哪个字段进行分类
        if l1_category_str:
            search_target_str = l1_category_str.lower()
        else:  # 如果一级品类为空，则使用二级品类
            search_target_str = l2_category_str.lower()

        # 3. 在选定的字段中进行关键词匹配
        if any(snack_keyword in search_target_str for snack_keyword in ['小吃', 'ăn vặt', 'hè']):
            found_code = 'cy0508'  # 默认为小吃->其他
            for keyword, code in snack_keyword_map.items():
                if keyword in search_target_str:
                    found_code = code;
                    break

        if not found_code:
            for keyword, code in keyword_map.items():
                if keyword.lower() in search_target_str:
                    found_code = code;
                    break

        # 4. 如果在选定字段中未找到任何匹配，则归为"其他"
        if not found_code:
            found_code = 'cy9999'

        # 5. 根据找到的二级品类，派生出一级品类并记录结果
        l2_code = found_code
        l1_code = l2_to_l1_code.get(l2_code, 'cy99')
        l1_name = l1_map.get(l1_code, '其他')
        l2_name = l2_map.get(l2_code, '其他')
        results.append((l1_code, l1_name, l2_code, l2_name))

    new_cols_df = pd.DataFrame(results, index=df.index,
                               columns=['new_一级品类_code', 'new_一级品类_name', 'new_二级品类_code',
                                        'new_二级品类_name'])
    return pd.concat([df, new_cols_df], axis=1)


# --- 主程序入口 ---
if __name__ == '__main__':
    # !!!重要!!! 请将下面的 'your_file.csv' 替换成您电脑上实际的文件名
    input_filename = 'all_shopeefood_restaurants_translated.csv'

    try:
        # 使用dtype=str确保所有列都以字符串形式读入，避免数字被错误解析
        df = pd.read_csv(input_filename, encoding='utf-8-sig', dtype=str).fillna('')
        print(f"成功读取文件: {input_filename}")
    except FileNotFoundError:
        print(f"错误：找不到文件 '{input_filename}'。请确认文件名是否正确，以及脚本和CSV文件是否在同一个文件夹下。")
        exit()
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        exit()

    l1_map, l2_map, keyword_map, snack_keyword_map = setup_mappings()
    processed_df = map_brand_category(df, l1_map, l2_map, keyword_map, snack_keyword_map)

    # 重新排列表格的列顺序
    original_cols = df.columns.tolist()
    new_cols = ['new_一级品类_code', 'new_一级品类_name', 'new_二级品类_code', 'new_二级品类_name']

    try:
        insertion_point = original_cols.index('类别') + 1
        final_col_order = original_cols[:insertion_point] + new_cols + original_cols[insertion_point:]
        processed_df = processed_df[final_col_order]
        print("\n已将新分类列调整到'二级品类'之后。")
    except ValueError:
        print("\n警告：未在原始文件中找到'二级品类'列，新分类将添加在文件末尾。")

    # 分离数据并保存
    classified_df = processed_df[processed_df['new_一级品类_code'] != 'cy99'].copy()
    other_df = processed_df[processed_df['new_一级品类_code'] == 'cy99'].copy()

    base_name, _ = os.path.splitext(input_filename)
    output_classified_filename = f"{base_name}_classified.csv"
    output_other_filename = f"{base_name}_others.csv"

    if not classified_df.empty:
        classified_df.to_csv(output_classified_filename, index=False, encoding='utf-8')
        print(f"\n处理完成！")
        print(f"已将 {len(classified_df)} 条明确分类的数据保存到: {output_classified_filename}")
    else:
        print("\n没有可以明确分类的数据。")

    if not other_df.empty:
        other_df.to_csv(output_other_filename, index=False, encoding='utf-8')
        print(f"已将 {len(other_df)} 条归为'其他'的数据保存到: {output_other_filename}")
    else:
        print("没有被归为'其他'类别的数据。")