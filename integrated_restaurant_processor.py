"""
餐厅数据智能处理器 - 整合爬取和翻译功能
集成了餐厅详情爬取和翻译功能，在爬取间隔期间自动处理翻译任务，提升整体处理效率。

主要特性：
- 异步翻译队列机制
- 智能缓存和批量处理
- 完整的错误处理和断点续传
- 实时翻译状态跟踪
"""

import json
import pandas as pd
import requests
import os
import time
import random
import csv
from datetime import datetime
from collections import deque
from typing import Dict, List, Optional, Tuple
from botasaurus import *
from botasaurus.browser_decorator import browser
from botasaurus_driver.user_agent import UserAgent


class TranslationManager:
    """翻译管理器 - 负责翻译队列、缓存和API调用"""
    
    def __init__(self, country: str, api_url: str = None, model_name: str = None):
        self.country = country
        self.api_url = api_url or 'http://113.98.195.218:1434/api/generate'
        self.model_name = model_name or 'qwen2.5-coder:32b'
        
        # 翻译队列和缓存
        self.translation_queue = deque()
        self.translation_cache = {}
        self.cache_file = f"{country}_translation_cache.json"
        
        # 批处理参数
        self.batch_size = 10
        self.max_queue_size = 50
        
        # 加载缓存
        self._load_cache()
        
        # 提示词模板
        self.prompt_template = """
你是一位顶级的品牌本地化专家和富有创造力的品牌命名大师，精通东南亚各国语言（包括越南语、马来语、印尼语、菲律宾语、泰语等）、英语和中文，对中国文化和消费者的审美有深刻的洞察。你的任务是处理一个品牌名称列表，将列表中的每一个品牌都转化为一个完美的、符合中国人阅读习惯和审美、且极具商业吸引力的简体中文品牌名。

对于输入列表中的【每一个】品牌，你都必须遵循以下【进阶决策流程】：

1.  **【最高优先级】解析品牌结构与意境**：
    *   首先，分析品牌名是否包含"主品牌 + 描述/副标题/地名"的结构（例如 `The East - The Taste Of Indochine` 或 `Ivegan Supershop Old Quater Hanoi`）。
    *   **禁止**对描述或副标题进行生硬的字面翻译。你的任务是【提炼】描述部分的【核心意境】（如"Indochine" -> 异域风情），并将其【艺术性地融合】进主品牌的中文名中。
    *   对于地名，如果它是品牌身份的一部分（如指代特定分店），应以符合中文商业习惯的方式自然地体现出来，例如使用括号作为后缀 `（XX店）`。

2.  **核实官方名称**：
    *   在确定了需要翻译的核心品牌和其意境后，检索并优先使用其官方或市场公认的中文名称。

3.  **融合"信达雅"进行艺术再创**：
    *   如果不存在官方名称，你需要进行艺术性的再创造，务必达到以下标准：
    *   **信 (Faithfulness)**：忠实于品牌的核心定位和产品属性（例如，餐厅、商店）。
    *   **达 (Expressiveness)**：中文名必须流畅、自然，易于口头传播，不能有任何西式语法或翻译腔。
    *   **雅 (Elegance)**：这是【最重要的标准】。名称必须优美、有格调，能引发积极的联想，符合中国人的文化审美。选择寓意美好、韵律和谐的汉字。

4.  **最终输出格式要求**：
    *   处理完输入列表中的【所有】品牌后，将得到的中文名称按照与输入列表完全相同的顺序，以【竖线 |】分隔，形成一个单一的字符串。
    *   **绝对禁止**：不要添加任何编号、引号、解释、分析过程或任何与中文名称无关的字符。

输入品牌名称列表（用'|'分隔）：{brands}
"""
    
    def _load_cache(self):
        """加载翻译缓存"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.translation_cache = json.load(f)
                print(f"✅ 加载翻译缓存: {len(self.translation_cache)} 条记录")
            except Exception as e:
                print(f"⚠️ 加载缓存失败: {e}")
                self.translation_cache = {}
    
    def _save_cache(self):
        """保存翻译缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.translation_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
    
    def add_to_queue(self, brand_name: str, row_index: int):
        """添加品牌名称到翻译队列"""
        if not brand_name or pd.isna(brand_name):
            return
            
        brand_name = str(brand_name).strip()
        if brand_name and brand_name not in self.translation_cache:
            self.translation_queue.append((brand_name, row_index))
            print(f"📝 添加到翻译队列: {brand_name}")
    
    def get_cached_translation(self, brand_name: str) -> Optional[str]:
        """从缓存获取翻译结果"""
        if not brand_name or pd.isna(brand_name):
            return None
        return self.translation_cache.get(str(brand_name).strip())
    
    def process_translation_batch(self) -> Dict[str, str]:
        """批量处理翻译队列"""
        if not self.translation_queue:
            return {}
        
        # 准备批次数据
        batch_items = []
        batch_size = min(self.batch_size, len(self.translation_queue))
        
        for _ in range(batch_size):
            if self.translation_queue:
                brand_name, row_index = self.translation_queue.popleft()
                if brand_name not in self.translation_cache:
                    batch_items.append((brand_name, row_index))
        
        if not batch_items:
            return {}
        
        # 提取品牌名称
        brands_to_translate = [item[0] for item in batch_items]
        print(f"🔄 开始批量翻译 {len(brands_to_translate)} 个品牌...")
        
        # 调用API进行翻译
        translated_results = self._call_translation_api(brands_to_translate)
        
        # 更新缓存和返回结果
        results = {}
        for i, (brand_name, row_index) in enumerate(batch_items):
            if i < len(translated_results) and translated_results[i]:
                translated_name = translated_results[i]
                self.translation_cache[brand_name] = translated_name
                results[brand_name] = translated_name
                print(f"✅ 翻译完成: {brand_name} -> {translated_name}")
            else:
                # 翻译失败，使用原名
                self.translation_cache[brand_name] = brand_name
                results[brand_name] = brand_name
                print(f"⚠️ 翻译失败，保留原名: {brand_name}")
        
        # 保存缓存
        self._save_cache()
        return results
    
    def _call_translation_api(self, brands: List[str]) -> List[str]:
        """调用翻译API"""
        if not brands:
            return []
        
        brands_string = "|".join(brands)
        prompt = self.prompt_template.format(brands=brands_string)
        
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False
        }
        headers = {'Content-Type': 'application/json'}
        
        try:
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=180)
            response.raise_for_status()
            response_json = response.json()
            output_text = response_json.get('response', '').strip()
            
            # 解析翻译结果
            translated_list = [name.strip() for name in output_text.split('|')]
            
            # 确保结果数量匹配
            if len(translated_list) == len(brands):
                return translated_list
            else:
                print(f"⚠️ API返回结果数量不匹配: 期望{len(brands)}, 实际{len(translated_list)}")
                # 补齐缺失的翻译
                while len(translated_list) < len(brands):
                    translated_list.append(brands[len(translated_list)])
                return translated_list[:len(brands)]
                
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            return brands  # 返回原始名称作为降级处理
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return len(self.translation_queue)
    
    def clear_queue(self):
        """清空翻译队列"""
        self.translation_queue.clear()


class DataManager:
    """数据管理器 - 负责数据保存、读取和状态管理"""
    
    def __init__(self, country: str):
        self.country = country
        self.output_file = f"{country}_restaurant_details_translated.csv"
        self.checkpoint_file = f"{country}_integrated_checkpoint.txt"
        
        # CSV字段定义
        self.fieldnames = [
            '城市', '品牌名称', '品牌名称_中文', '评分', '平均价格', 
            '一级品类', '二级品类', '评论数目', '电话', '邮箱', 
            '品牌链接', '地址', '翻译状态', '翻译时间'
        ]
    
    def load_checkpoint(self) -> int:
        """加载断点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    return int(f.read().strip())
            except:
                print(f"⚠️ 无法读取断点文件，从头开始")
        return 0
    
    def save_checkpoint(self, index: int):
        """保存断点"""
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                f.write(str(index))
        except Exception as e:
            print(f"❌ 保存断点失败: {e}")
    
    def save_restaurant_data(self, data: Dict, translated_name: str = None):
        """保存餐厅数据"""
        # 准备数据
        row_data = {
            '城市': data.get('城市', ''),
            '品牌名称': data.get('品牌名称', ''),
            '品牌名称_中文': translated_name or data.get('品牌名称', ''),
            '评分': data.get('评分', ''),
            '平均价格': data.get('平均价格', ''),
            '一级品类': data.get('一级品类', ''),
            '二级品类': data.get('二级品类', ''),
            '评论数目': data.get('评论数目', ''),
            '电话': data.get('电话', ''),
            '邮箱': data.get('邮箱', ''),
            '品牌链接': data.get('品牌链接', ''),
            '地址': data.get('地址', ''),
            '翻译状态': 'completed' if translated_name else 'pending',
            '翻译时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S') if translated_name else ''
        }
        
        try:
            # 检查文件是否存在
            file_exists = os.path.exists(self.output_file) and os.path.getsize(self.output_file) > 0
            
            with open(self.output_file, 'a', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=self.fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                writer.writerow(row_data)
                f.flush()
                
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def update_translation_status(self, brand_name: str, translated_name: str):
        """更新翻译状态（用于后续批量更新）"""
        # 这里可以实现更复杂的状态更新逻辑
        # 目前在save_restaurant_data中直接处理
        pass


class IntegratedRestaurantProcessor:
    """餐厅数据智能处理器 - 主处理类"""

    def __init__(self, country: str, **kwargs):
        self.country = country
        self.restaurant_urls_file = f"{country}_restaurant_urls.csv"

        # 处理参数
        self.requests_per_minute = kwargs.get('requests_per_minute', 3)
        self.min_delay = 60 / self.requests_per_minute
        self.batch_size = kwargs.get('batch_size', 10)

        # 初始化管理器
        self.translation_manager = TranslationManager(
            country=country,
            api_url=kwargs.get('api_url'),
            model_name=kwargs.get('model_name')
        )
        self.data_manager = DataManager(country)

        print(f"🚀 初始化餐厅数据智能处理器")
        print(f"📍 国家: {country}")
        print(f"⏱️ 处理频率: {self.requests_per_minute} 请求/分钟")
        print(f"📦 翻译批次大小: {self.batch_size}")

    def load_restaurant_urls(self) -> List[Dict]:
        """加载餐厅URL列表"""
        restaurant_urls = []
        try:
            with open(self.restaurant_urls_file, 'r', encoding='utf-8', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if 'city' in row and 'url' in row:
                        restaurant_urls.append({
                            'city': row['city'],
                            'url': row['url'].split('#')[0]
                        })
            print(f"✅ 成功加载 {len(restaurant_urls)} 个餐厅URL")
            return restaurant_urls
        except FileNotFoundError:
            print(f"❌ 无法找到文件 '{self.restaurant_urls_file}'")
            return []
        except Exception as e:
            print(f"❌ 读取文件时出错: {e}")
            return []

    def process_restaurants(self):
        """主处理函数 - 爬取餐厅详情并异步处理翻译"""
        print(f"🌍 开始处理国家: {self.country}")

        # 调用带浏览器装饰器的处理函数
        return process_restaurants_with_browser(self.country)


@browser(
    user_agent=UserAgent.REAL,
    chrome_executable_path=r"C:\Program Files\Google\Chrome\Application\chrome.exe",
    cache=True,
    block_images=True,
    lang="zh-CN",
    reuse_driver=True,
    close_on_crash=True,
    create_error_logs=True,
    headless=False,
)
def process_restaurants_with_browser(driver, country):
    """带浏览器装饰器的餐厅处理函数"""
    # 创建处理器实例
    processor = IntegratedRestaurantProcessor(
        country=country,
        batch_size=10,
        requests_per_minute=3
    )

    # 加载餐厅URL列表
    restaurant_urls = processor.load_restaurant_urls()
    if not restaurant_urls:
        print("❌ 没有可处理的餐厅URL，程序退出")
        return

    # 加载断点
    start_index = processor.data_manager.load_checkpoint()
    if start_index >= len(restaurant_urls):
        print("✅ 所有URL已处理完成")
        return

    print(f"📍 从索引 {start_index} 开始处理，共 {len(restaurant_urls)} 个URL")

    # 处理每个餐厅
    for index in range(start_index, len(restaurant_urls)):
        task = restaurant_urls[index]
        city = task['city']
        url = task['url']

        print(f"\n--- 🏪 处理餐厅 {index + 1}/{len(restaurant_urls)}: {city} ---")
        print(f"🔗 URL: {url}")

        try:
            # 爬取餐厅详情
            restaurant_data = scrape_restaurant_details(driver, city, url)

            if restaurant_data:
                # 检查是否有缓存的翻译
                brand_name = restaurant_data.get('品牌名称', '')
                cached_translation = processor.translation_manager.get_cached_translation(brand_name)

                if cached_translation:
                    # 使用缓存的翻译
                    print(f"💾 使用缓存翻译: {brand_name} -> {cached_translation}")
                    processor.data_manager.save_restaurant_data(restaurant_data, cached_translation)
                else:
                    # 添加到翻译队列
                    processor.translation_manager.add_to_queue(brand_name, index)
                    # 先保存原始数据
                    processor.data_manager.save_restaurant_data(restaurant_data)

                print(f"✅ 数据保存完成: {brand_name}")

            # 更新断点
            processor.data_manager.save_checkpoint(index + 1)

            # 在等待间隔期间处理翻译队列
            process_translations_during_delay(processor)

        except Exception as e:
            print(f"❌ 处理餐厅时出错: {e}")
            continue

    # 处理剩余的翻译队列
    process_remaining_translations(processor)
    print("🎉 所有餐厅处理完成！")


def process_translations_during_delay(processor):
    """在等待间隔期间处理翻译队列"""
    delay_start = time.time()

    # 处理翻译队列
    while (time.time() - delay_start) < processor.min_delay and processor.translation_manager.get_queue_size() > 0:
        if processor.translation_manager.get_queue_size() >= processor.batch_size:
            print(f"🔄 间隔期间处理翻译批次...")
            processor.translation_manager.process_translation_batch()
        else:
            time.sleep(1)  # 等待更多项目加入队列

    # 确保满足最小延时要求
    remaining_delay = processor.min_delay - (time.time() - delay_start)
    if remaining_delay > 0:
        print(f"⏳ 等待剩余时间: {remaining_delay:.1f}秒")
        time.sleep(remaining_delay)


def process_remaining_translations(processor):
    """处理剩余的翻译队列"""
    print(f"🔄 处理剩余翻译队列: {processor.translation_manager.get_queue_size()} 项")

    while processor.translation_manager.get_queue_size() > 0:
        processor.translation_manager.process_translation_batch()
        time.sleep(1)  # 避免API调用过于频繁


def scrape_restaurant_details(driver, city: str, url: str) -> Optional[Dict]:
        """爬取餐厅详情数据"""
        try:
            driver.get(url)
            time.sleep(random.uniform(2, 4))

            data = {
                '城市': city.replace('餐厅', ''),
                '品牌链接': url
            }

            # 品牌名称
            try:
                brand_name = driver.select("h1.biGQs._P.hzzSG").text
                data['品牌名称'] = brand_name
            except:
                data['品牌名称'] = ''
                print("⚠️ 无法提取品牌名称")

            # 评分
            try:
                rating = driver.select('div[data-automation="bubbleRatingValue"]').text
                data['评分'] = rating
            except:
                data['评分'] = ''

            # 客单均价
            try:
                price_range_text = driver.select("span.HUMGB.cPbcf span.bTeln:last-of-type a").text
                if '-' in price_range_text and any(char.isdigit() for char in price_range_text):
                    price_range_text = price_range_text.replace('¥', '')
                    prices = []
                    for p in price_range_text.split('-'):
                        cleaned_p = ''.join(filter(str.isdigit, p))
                        if cleaned_p:
                            prices.append(int(cleaned_p))

                    if prices:
                        data['平均价格'] = sum(prices) / len(prices)
                    else:
                        data['平均价格'] = price_range_text
                else:
                    data['平均价格'] = price_range_text
            except:
                data['平均价格'] = ''

            # 一级品类
            try:
                category = driver.select("span.HUMGB.cPbcf a:last-of-type").text
                data['一级品类'] = category
            except:
                data['一级品类'] = ''

            # 二级品类
            try:
                category_lv2 = driver.select_all("span.HUMGB.cPbcf a")[1].text
                data['二级品类'] = category_lv2
            except:
                data['二级品类'] = ''

            # 评论数目
            try:
                review_count_text = driver.select(
                    'div.CsAqy a[href="#REVIEWS"] div[data-automation="bubbleReviewCount"]').text
                review_count = ''.join(filter(str.isdigit, review_count_text))
                data['评论数目'] = review_count
            except:
                data['评论数目'] = ''

            # 电话
            try:
                phone = driver.select("a[href^='tel:']").text.replace(' ', '')
                data['电话'] = phone
            except:
                data['电话'] = ''

            # 地址
            try:
                address = driver.select("button.Tbrbj span").text
                data['地址'] = address
            except:
                data['地址'] = ''

            # 邮箱
            try:
                mail = driver.select("a[href^='mailto:']").get_attribute('href')
                data['邮箱'] = mail.replace('mailto:', '')
            except:
                data['邮箱'] = ''

            return data

        except Exception as e:
            print(f"❌ 爬取餐厅详情失败: {e}")
            return None


def main():
    """主函数 - 命令行入口"""
    import argparse

    parser = argparse.ArgumentParser(description='餐厅数据智能处理器')
    parser.add_argument('--country', type=str, default='菲律宾', help='要处理的国家')
    parser.add_argument('--countries', type=str, help='要处理的多个国家，用逗号分隔')
    parser.add_argument('--batch-size', type=int, default=10, help='翻译批次大小')
    parser.add_argument('--delay', type=int, default=20, help='请求间隔时间（秒）')
    parser.add_argument('--api-url', type=str, help='翻译API地址')
    parser.add_argument('--model-name', type=str, help='翻译模型名称')

    args = parser.parse_args()

    # 确定要处理的国家列表
    if args.countries:
        countries = [c.strip() for c in args.countries.split(',')]
    elif ',' in args.country:
        # 支持在country参数中使用逗号分隔
        countries = [c.strip() for c in args.country.split(',')]
    else:
        countries = [args.country]

    print(f"🎯 开始处理国家: {', '.join(countries)}")

    # 处理每个国家
    for country in countries:
        print(f"\n{'='*50}")
        print(f"🌍 开始处理国家: {country}")
        print(f"{'='*50}")

        # 检查餐厅URL文件是否存在
        restaurant_urls_file = f"{country}_restaurant_urls.csv"
        if not os.path.exists(restaurant_urls_file):
            print(f"❌ 找不到餐厅URL文件: {restaurant_urls_file}")
            continue

        # 创建处理器实例
        processor = IntegratedRestaurantProcessor(
            country=country,
            batch_size=args.batch_size,
            requests_per_minute=60 / args.delay if args.delay else 3,
            api_url=args.api_url,
            model_name=args.model_name
        )

        # 开始处理
        try:
            processor.process_restaurants()
            print(f"✅ {country} 处理完成")
        except Exception as e:
            print(f"❌ {country} 处理失败: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n🎉 所有国家处理完成！")


if __name__ == "__main__":
    main()
