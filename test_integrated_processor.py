"""
测试脚本 - 验证餐厅数据智能处理器功能
使用少量数据测试整合后的功能，验证爬取、翻译和数据保存是否正常
"""

import os
import csv
import json
import time
from integrated_restaurant_processor import IntegratedRestaurantProcessor, TranslationManager, DataManager


def create_test_data():
    """创建测试用的小数据集"""
    test_file = "菲律宾_test_restaurant_urls.csv"
    
    # 从原始文件中提取前5条数据作为测试
    with open("菲律宾_restaurant_urls.csv", 'r', encoding='utf-8') as source:
        reader = csv.DictReader(source)
        rows = list(reader)[:5]  # 只取前5条
    
    # 写入测试文件
    with open(test_file, 'w', encoding='utf-8', newline='') as test:
        if rows:
            writer = csv.DictWriter(test, fieldnames=rows[0].keys())
            writer.writeheader()
            writer.writerows(rows)
    
    print(f"✅ 创建测试数据文件: {test_file} ({len(rows)} 条记录)")
    return test_file


def test_translation_manager():
    """测试翻译管理器功能"""
    print("\n🧪 测试翻译管理器...")
    
    tm = TranslationManager("菲律宾_test")
    
    # 测试添加到队列
    test_brands = [
        "AKITA Japanese Restaurant",
        "Scape Skydeck", 
        "Cabana Grill and Seafood"
    ]
    
    for i, brand in enumerate(test_brands):
        tm.add_to_queue(brand, i)
    
    print(f"📝 队列大小: {tm.get_queue_size()}")
    
    # 测试缓存功能
    tm.translation_cache["Test Brand"] = "测试品牌"
    cached = tm.get_cached_translation("Test Brand")
    print(f"💾 缓存测试: {cached}")
    
    # 测试批量翻译（模拟）
    if tm.get_queue_size() > 0:
        print("🔄 测试批量翻译...")
        # 注意：这里会调用真实的API，如果API不可用会降级处理
        results = tm.process_translation_batch()
        print(f"✅ 翻译结果: {len(results)} 条")
        for original, translated in results.items():
            print(f"  {original} -> {translated}")
    
    print("✅ 翻译管理器测试完成")


def test_data_manager():
    """测试数据管理器功能"""
    print("\n🧪 测试数据管理器...")
    
    dm = DataManager("菲律宾_test")
    
    # 测试保存数据
    test_data = {
        '城市': '宿雾',
        '品牌名称': 'Test Restaurant',
        '评分': '4.5',
        '平均价格': '100',
        '一级品类': '日式料理',
        '二级品类': '寿司',
        '评论数目': '50',
        '电话': '+63123456789',
        '邮箱': '<EMAIL>',
        '品牌链接': 'https://example.com',
        '地址': 'Test Address'
    }
    
    dm.save_restaurant_data(test_data, "测试餐厅")
    print("✅ 数据保存测试完成")
    
    # 测试断点功能
    dm.save_checkpoint(10)
    checkpoint = dm.load_checkpoint()
    print(f"📍 断点测试: {checkpoint}")
    
    print("✅ 数据管理器测试完成")


def test_integrated_processor():
    """测试整合处理器（不运行浏览器）"""
    print("\n🧪 测试整合处理器初始化...")
    
    # 创建测试数据
    test_file = create_test_data()
    
    # 临时修改文件名以使用测试数据
    original_file = "菲律宾_restaurant_urls.csv"
    backup_file = "菲律宾_restaurant_urls_backup.csv"
    
    try:
        # 备份原文件
        if os.path.exists(original_file):
            os.rename(original_file, backup_file)
        
        # 使用测试文件
        os.rename(test_file, original_file)
        
        # 创建处理器实例
        processor = IntegratedRestaurantProcessor(
            country="菲律宾",
            batch_size=3,
            requests_per_minute=6  # 更快的测试速度
        )
        
        # 测试URL加载
        urls = processor.load_restaurant_urls()
        print(f"📂 加载URL数量: {len(urls)}")
        
        # 显示加载的URL
        for i, url_data in enumerate(urls):
            print(f"  {i+1}. {url_data['city']} - {url_data['url'][:50]}...")
        
        print("✅ 整合处理器初始化测试完成")
        
    finally:
        # 恢复原文件
        if os.path.exists(original_file):
            os.remove(original_file)
        if os.path.exists(backup_file):
            os.rename(backup_file, original_file)
        if os.path.exists(test_file):
            os.remove(test_file)


def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "菲律宾_test_restaurant_details_translated.csv",
        "菲律宾_test_translation_cache.json",
        "菲律宾_test_integrated_checkpoint.txt",
        "菲律宾_test_restaurant_urls.csv"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ 清理测试文件: {file}")


def main():
    """主测试函数"""
    print("🚀 开始测试餐厅数据智能处理器")
    print("="*50)
    
    try:
        # 运行各项测试
        test_translation_manager()
        test_data_manager()
        test_integrated_processor()
        
        print("\n" + "="*50)
        print("🎉 所有测试完成！")
        
        # 显示生成的测试文件
        print("\n📁 生成的测试文件:")
        test_files = [
            "菲律宾_test_restaurant_details_translated.csv",
            "菲律宾_test_translation_cache.json",
            "菲律宾_test_integrated_checkpoint.txt"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"  ✅ {file} ({size} bytes)")
            else:
                print(f"  ❌ {file} (未生成)")
        
        # 询问是否清理测试文件
        print(f"\n🤔 是否清理测试文件？(y/n): ", end="")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 可选择性清理
        print("\n💡 测试完成。如需清理测试文件，请手动运行 cleanup_test_files()")


if __name__ == "__main__":
    main()
